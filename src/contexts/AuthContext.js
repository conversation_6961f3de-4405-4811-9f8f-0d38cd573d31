import React, { createContext, useState, useContext, useEffect } from 'react';
import { supabase } from '../services/supabase';
import * as authService from '../services/auth';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check active sessions and sets the user
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for changes on auth state
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const value = {
    signUp: (data) => authService.signUp(data.email, data.password),
    signIn: (data) => authService.signIn(data.email, data.password),
    signOut: () => authService.signOut(),
    resetPassword: (email) => authService.resetPassword(email),
    resendEmailConfirmation: (email) => authService.resendEmailConfirmation(email),
    user,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  return useContext(AuthContext);
};