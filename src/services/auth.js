import { supabase } from './supabase';

export const AUTH_ERRORS = {
  INVALID_EMAIL: 'Please enter a valid email address',
  WEAK_PASSWORD: 'Password must be at least 8 characters long and include a number',
  EMAIL_IN_USE: 'This email is already registered. Try signing in instead.',
  INVALID_CREDENTIALS: 'Invalid email or password. Please check your credentials and try again.',
  USER_NOT_FOUND: 'No account found with this email. Would you like to create an account?',
  EMAIL_NOT_CONFIRMED: 'Please check your email and click the verification link before signing in.',
  TOO_MANY_REQUESTS: 'Too many attempts. Please wait a moment before trying again.',
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  SIGNUP_DISABLED: 'New registrations are currently disabled.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
};

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password) => {
  return password.length >= 8 && /\d/.test(password);
};

// Helper function to parse Supabase auth errors
const parseAuthError = (error) => {
  if (!error) return AUTH_ERRORS.UNKNOWN_ERROR;

  const message = error.message?.toLowerCase() || '';

  if (message.includes('invalid login credentials') || message.includes('invalid email or password')) {
    return AUTH_ERRORS.INVALID_CREDENTIALS;
  }
  if (message.includes('user not found') || message.includes('email not found')) {
    return AUTH_ERRORS.USER_NOT_FOUND;
  }
  if (message.includes('email not confirmed') || message.includes('email address not confirmed')) {
    return AUTH_ERRORS.EMAIL_NOT_CONFIRMED;
  }
  if (message.includes('already registered') || message.includes('user already registered')) {
    return AUTH_ERRORS.EMAIL_IN_USE;
  }
  if (message.includes('too many requests') || message.includes('rate limit')) {
    return AUTH_ERRORS.TOO_MANY_REQUESTS;
  }
  if (message.includes('signup is disabled') || message.includes('signups not allowed')) {
    return AUTH_ERRORS.SIGNUP_DISABLED;
  }
  if (message.includes('network') || message.includes('fetch')) {
    return AUTH_ERRORS.NETWORK_ERROR;
  }

  return error.message || AUTH_ERRORS.UNKNOWN_ERROR;
};

export const signUp = async (email, password) => {
  try {
    // Validate inputs
    if (!validateEmail(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }
    if (!validatePassword(password)) {
      throw new Error(AUTH_ERRORS.WEAK_PASSWORD);
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (error) {
      throw new Error(parseAuthError(error));
    }

    // Check if user needs email confirmation
    const needsConfirmation = data.user && !data.session;

    return {
      success: true,
      message: needsConfirmation
        ? 'Account created! Please check your email for a verification link before signing in.'
        : 'Account created successfully! You are now signed in.',
      data,
      needsEmailConfirmation: needsConfirmation,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || AUTH_ERRORS.UNKNOWN_ERROR,
      error,
    };
  }
};

export const signIn = async (email, password) => {
  try {
    // Validate email
    if (!validateEmail(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(parseAuthError(error));
    }

    // Check if user's email is confirmed
    if (data.user && !data.user.email_confirmed_at) {
      return {
        success: false,
        message: AUTH_ERRORS.EMAIL_NOT_CONFIRMED,
        error: { type: 'email_not_confirmed' },
        needsEmailConfirmation: true,
      };
    }

    return {
      success: true,
      message: `Welcome back, ${data.user?.email?.split('@')[0] || 'there'}!`,
      data,
    };
  } catch (error) {
    const errorMessage = error.message || AUTH_ERRORS.UNKNOWN_ERROR;

    return {
      success: false,
      message: errorMessage,
      error,
      // Suggest sign up if user not found
      suggestSignUp: errorMessage === AUTH_ERRORS.USER_NOT_FOUND ||
                    errorMessage === AUTH_ERRORS.INVALID_CREDENTIALS,
    };
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;

    return {
      success: true,
      message: 'Successfully signed out',
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || AUTH_ERRORS.UNKNOWN_ERROR,
      error,
    };
  }
};

export const resetPassword = async (email) => {
  try {
    if (!validateEmail(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }

    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;

    return {
      success: true,
      message: 'Password reset instructions sent to your email',
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || AUTH_ERRORS.UNKNOWN_ERROR,
      error,
    };
  }
};

export const resendEmailConfirmation = async (email) => {
  try {
    if (!validateEmail(email)) {
      throw new Error(AUTH_ERRORS.INVALID_EMAIL);
    }

    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: email,
    });

    if (error) {
      throw new Error(parseAuthError(error));
    }

    return {
      success: true,
      message: 'Verification email sent! Please check your inbox and spam folder.',
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || AUTH_ERRORS.UNKNOWN_ERROR,
      error,
    };
  }
};